---
# 注意不要修改本文头文件，如修改，CodeBuddy（内网版）将按照默认逻辑设置
type: manual
---

# my-flow
## 背景信息
1. 我的代码仓库是 https://git.woa.com/jonnytang/copilot-demo-01
2. 工蜂项目 id 是 jonnytang/copilot-demo-01

## 帮我完成以下事项
1. 我会给你一个工蜂 issue 的 id，如果没有给，请询问我，并终止任务
2. 拉取我指定 issue id 的内容（可借助 gongfeng get_issue_detail 工具完成）
3. 创建一个新的分支，结合 issue 内容改写我的代码
4. 评审以上修改的代码是否是否有安全问题，有的话请修复
5. 将以上修改提交到远程，并发起一个新的 gongfeng mr（可借助 gongfeng create_merge_request 工具完成）
6. 最后总结一下所有内容

