---
# 注意不要修改本文头文件，如修改，CodeBuddy（内网版）将按照默认逻辑设置
type: manual
---
1. 我会给你一个 mcp 的 json 的配置，你需要帮我合并到到 mcp.json 文件中
我给你的配置大概会是：

```json
{
  "mcpServers": {
    "mcp-cc": {
      "command": "npx",
      "args": [
        "-y",
        "@tencent/zhiyan_paas_mcp@latest"
      ],
      "env": {
        "TOKEN": "my-token"
      },
      "disabled": false,
      "timeout": 100000
    }
  }
}
```

需要合并到的目标 mcp.json，这个 mcp.json 可能是：
```json
{
  "mcpServers": {
    "mcp-aa": {
      "command": "uvx",
      "args": [
        "mcp-server-fetch",
        "--ignore-robots-txt"
      ]
    },
    "mcp-bb": {
      "url": "http://trpc-mcp-server.woa.com/sse",
      "disabled": true
    }
  }
}
```

合并后的样子：
```json
{
  "mcpServers": {
    "mcp-aa": {
      "command": "uvx",
      "args": [
        "mcp-server-fetch",
        "--ignore-robots-txt"
      ]
    },
    "mcp-bb": {
      "url": "http://trpc-mcp-server.woa.com/sse",
      "disabled": true
    },
    "mcp-cc": {
      "command": "npx",
      "args": [
        "-y",
        "@tencent/zhiyan_paas_mcp@latest"
      ],
      "env": {
        "TOKEN": "my-token"
      },
      "disabled": false,
      "timeout": 100000
    }
  }
}
```

假如有需要填写 token 或者环境变量的地方可以让用户补充