package models

import (
	"errors"
	"time"
)

// OrderStatus 订单状态
type OrderStatus string

const (
	OrderStatusPending   OrderStatus = "pending"
	OrderStatusPaid      OrderStatus = "paid"
	OrderStatusShipped   OrderStatus = "shipped"
	OrderStatusDelivered OrderStatus = "delivered"
	OrderStatusCanceled  OrderStatus = "canceled"
)

// Order 订单结构体
type Order struct {
	ID            string      `json:"id"`
	UserID        string      `json:"user_id"`
	TotalAmount   float64     `json:"total_amount"`
	Status        OrderStatus `json:"status"`
	Items         []OrderItem `json:"items"`
	CreatedAt     time.Time   `json:"created_at"`
	LastUpdatedAt time.Time   `json:"last_updated_at"`
}

// OrderItem 订单项结构体
type OrderItem struct {
	ProductID   string  `json:"product_id"`
	Quantity    int     `json:"quantity"`
	UnitPrice   float64 `json:"unit_price"`
	TotalPrice  float64 `json:"total_price"`
}

// Validate 验证订单基本信息
func (o *Order) Validate() error {
	if o.UserID == "" {
		return errors.New("user ID is required")
	}

	if len(o.Items) == 0 {
		return errors.New("order must contain at least one item")
	}

	if o.TotalAmount <= 0 {
		return errors.New("total amount must be greater than 0")
	}

	for _, item := range o.Items {
		if err := item.Validate(); err != nil {
			return err
		}
	}

	return nil
}

// Validate 验证订单项
func (i *OrderItem) Validate() error {
	if i.ProductID == "" {
		return errors.New("product ID is required")
	}

	if i.Quantity <= 0 {
		return errors.New("quantity must be greater than 0")
	}

	if i.UnitPrice <= 0 {
		return errors.New("unit price must be greater than 0")
	}

	if i.TotalPrice != float64(i.Quantity)*i.UnitPrice {
		return errors.New("total price does not match quantity and unit price")
	}

	return nil
}
