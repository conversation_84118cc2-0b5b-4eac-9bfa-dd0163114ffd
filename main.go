package main

import (
	"log"
	"time"

	"github.com/tangcaijun/copilot-demo-01/models"
	"github.com/tangcaijun/copilot-demo-01/services"
)

func main() {
	log.Println("开始订单处理程序")
	startTime := time.Now()

	orders := []models.Order{
		{
			ID:          "1",
			UserID:      "101",
			TotalAmount: 99.9,
			Status:      models.OrderStatusPending,
			Items: []models.OrderItem{
				{
					ProductID:  "1001",
					Quantity:   1,
					UnitPrice:  99.9,
					TotalPrice: 99.9,
				},
			},
			CreatedAt:     time.Now().Add(-24 * time.Hour),
			LastUpdatedAt: time.Now(),
		},
		{
			ID:          "2",
			UserID:      "102",
			TotalAmount: 199.9,
			Status:      models.OrderStatusPending,
			Items: []models.OrderItem{
				{
					ProductID:  "1002",
					Quantity:   1,
					UnitPrice:  199.9,
					TotalPrice: 199.9,
				},
				{
					ProductID:  "1003",
					Quantity:   2,
					UnitPrice:  50.0,
					TotalPrice: 100.0,
				},
			},
			CreatedAt:     time.Now().Add(-23 * time.Hour),
			LastUpdatedAt: time.Now(),
		},
		{
			ID:            "3",
			UserID:        "103",
			TotalAmount:   50.0,
			Status:        models.OrderStatusPending,
			Items:         []models.OrderItem{},
			CreatedAt:     time.Now().Add(-22 * time.Hour),
			LastUpdatedAt: time.Now(),
		},
		{
			ID:          "4",
			UserID:      "104",
			TotalAmount: 150.0,
			Status:      models.OrderStatusPending,
			Items: []models.OrderItem{
				{
					ProductID:  "1004",
					Quantity:   1,
					UnitPrice:  100.0,
					TotalPrice: 100.0,
				},
			},
			CreatedAt:     time.Now().Add(-21 * time.Hour),
			LastUpdatedAt: time.Now(),
		},
	}

	log.Printf("创建了 %d 个测试订单", len(orders))

	orderService := services.NewOrderService()
	if err := orderService.ProcessOrders(orders); err != nil {
		log.Printf("订单处理遇到错误: %v", err)
	} else {
		log.Println("所有订单处理成功")
	}

	elapsed := time.Since(startTime)
	log.Printf("订单处理完成, 耗时: %s", elapsed)
}
