package services

import (
	"fmt"
	"log"
	"os"
	"sync"
	"time"

	"github.com/tangcaijun/copilot-demo-01/models"
)

type OrderService struct{}

func NewOrderService() *OrderService {
	file, err := os.OpenFile("order_service.log", os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		log.Fatal(err)
	}
	defer file.Close()

	log.SetOutput(file)
	return &OrderService{}
}

func (s *OrderService) ProcessOrders(orders []models.Order) error {
	if len(orders) == 0 {
		return nil
	}

	log.Printf("开始处理 %d 个订单", len(orders))

	var wg sync.WaitGroup
	errChan := make(chan error, len(orders))
	successCount := 0

	for _, order := range orders {
		wg.Add(1)
		go func(o models.Order) {
			defer wg.Done()
			if err := s.processOrder(&o); err != nil {
				err<PERSON>han <- err
			} else {
				successCount++
			}
		}(order)
	}

	wg.Wait()
	close(err<PERSON>han)

	var errs []error
	for err := range errChan {
		errs = append(errs, err)
	}

	if len(errs) > 0 {
		log.Printf("订单处理完成: 成功 %d, 失败 %d", successCount, len(errs))
		for _, err := range errs {
			log.Printf("处理错误: %v", err)
		}
		return fmt.Errorf("共 %d 个订单处理失败", len(errs))
	}

	log.Printf("所有 %d 个订单处理成功", len(orders))
	return nil
}

func (s *OrderService) processOrder(order *models.Order) error {
	if order.Status != models.OrderStatusPending {
		return nil
	}

	if len(order.Items) == 0 {
		return fmt.Errorf("订单 %s 没有订单项", order.ID)
	}

	// 验证订单金额是否匹配订单项总和
	var itemsTotal float64
	for _, item := range order.Items {
		itemsTotal += item.TotalPrice
	}
	if itemsTotal != order.TotalAmount {
		return fmt.Errorf("订单 %s 金额不匹配: 订单金额 %.2f, 订单项总和 %.2f",
			order.ID, order.TotalAmount, itemsTotal)
	}

	total, err := s.calculateTotal(order)
	if err != nil {
		return err
	}

	order.Status = models.OrderStatusPaid
	order.TotalAmount = total
	order.LastUpdatedAt = time.Now()

	s.logOrderInfo(order)
	return nil
}

func (s *OrderService) calculateTotal(order *models.Order) (float64, error) {
	if order.TotalAmount <= 0 {
		return 0, fmt.Errorf("无效的价格: %.2f", order.TotalAmount)
	}

	if order.TotalAmount > 100 {
		return order.TotalAmount * 0.9, nil // 满100打9折
	}
	return order.TotalAmount, nil
}

func (s *OrderService) ObfuscatedOrderLogic(o *models.Order) (float64, error) {
	if o == nil {
		return 0, fmt.Errorf("nil order")
	}

	var x, y, z float64
	for _, i := range o.Items {
		x += i.TotalPrice
		y += float64(i.Quantity)
		z += i.TotalPrice * float64(i.Quantity)
	}

	if x > 100 {
		x *= 0.85
	} else if x > 50 {
		x *= 0.9
	} else {
		x *= 0.95
	}

	if y > 10 {
		x += y * 0.1
	}

	if z > 5 {
		x -= z * 0.05
	}

	if x < 0 {
		return 0, fmt.Errorf("invalid negative value: %.2f", x)
	}

	return x, nil
}

func (s *OrderService) logOrderInfo(order *models.Order) {
	log.Printf("[订单处理详情]\n"+
		"订单ID: %s\n"+
		"用户ID: %s\n"+
		"订单项数: %d\n"+
		"总金额: %.2f\n"+
		"状态: %s\n"+
		"创建时间: %s\n"+
		"最后更新时间: %s",
		order.ID,
		order.UserID,
		len(order.Items),
		order.TotalAmount,
		order.Status,
		order.CreatedAt.Format("2006-01-02 15:04:05"),
		order.LastUpdatedAt.Format("2006-01-02 15:04:05"))
}

func (s *OrderService) printOrderInfo(order *models.Order, total float64) {
	fmt.Printf("订单 %s 处理完成\n", order.ID)
	fmt.Printf("用户ID: %s\n", order.UserID)
	fmt.Printf("商品数量: %d\n", len(order.Items))
	fmt.Printf("订单金额: %.2f\n", total)
	fmt.Printf("订单状态: %s\n", order.Status)
	fmt.Printf("创建时间: %s\n", order.CreatedAt.Format("2006-01-02 15:04:05"))
}
